'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'

interface AudioPlayerProps {
  audioUrl: string
  duration: number

  className?: string
  waveformData?: number[] // Optional pre-computed waveform
  postId?: string // For tracking plays
  onPlayCountUpdate?: (count: number) => void
}

export function AudioPlayer({
  audioUrl,
  duration,

  className = '',
  waveformData,
  postId,
  onPlayCountUpdate
}: AudioPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [audioLevel, setAudioLevel] = useState(0)
  
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const analyserRef = useRef<AnalyserNode | null>(null)
  const animationRef = useRef<number | null>(null)

  // Generate simple waveform bars if no data provided
  const waveform = waveformData || Array.from({ length: 40 }, () => Math.random() * 0.8 + 0.2)

  useEffect(() => {
    // Create audio element

    audioRef.current = new Audio(audioUrl)
    const audio = audioRef.current

    // Ensure volume is set to maximum
    audio.volume = 1.0

    audio.addEventListener('loadstart', () => {
      console.log('Audio loadstart')
      setIsLoading(true)
    })
    audio.addEventListener('canplay', () => {
      console.log('Audio canplay')
      setIsLoading(false)
    })
    audio.addEventListener('error', (e) => {
      // Only log actual errors, not normal events
      if (e.type === 'error' && audio.error) {
        console.error('Audio playback error:', audio.error.message, 'URL:', audioUrl)
      }
      setIsLoading(false)
    })
    audio.addEventListener('loadeddata', () => {
      console.log('Audio loadeddata')
    })
    audio.addEventListener('timeupdate', () => {
      setCurrentTime(audio.currentTime)
    })
    audio.addEventListener('ended', () => {
      setIsPlaying(false)
      setCurrentTime(0)
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    })



    return () => {
      if (audio) {
        audio.pause()
        audio.src = ''
      }
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [audioUrl])

  const setupAudioContext = async () => {
    if (!audioRef.current || audioContextRef.current) return

    try {
      audioContextRef.current = new AudioContext()
      analyserRef.current = audioContextRef.current.createAnalyser()
      const source = audioContextRef.current.createMediaElementSource(audioRef.current)
      source.connect(analyserRef.current)
      analyserRef.current.connect(audioContextRef.current.destination)
      analyserRef.current.fftSize = 256
    } catch (error) {
      console.error('Error setting up audio context:', error)
    }
  }

  const updateAudioLevel = () => {
    if (!analyserRef.current) return

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount)
    analyserRef.current.getByteFrequencyData(dataArray)
    
    const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length
    setAudioLevel(average / 255)

    if (isPlaying) {
      animationRef.current = requestAnimationFrame(updateAudioLevel)
    }
  }

  const handlePlay = async () => {
    if (!audioRef.current) return

    try {
      if (isPlaying) {
        audioRef.current.pause()
        setIsPlaying(false)
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current)
        }
      } else {
        // Simple play without Web Audio API
        await audioRef.current.play()
        setIsPlaying(true)
        // updateAudioLevel() // Disable for now to test
      }
    } catch (error) {
      console.error('Error playing audio:', error)
      setIsPlaying(false)
    }
  }

  const handleSeek = (clickX: number, elementWidth: number) => {
    if (!audioRef.current) return
    
    const seekTime = (clickX / elementWidth) * duration
    audioRef.current.currentTime = seekTime
    setCurrentTime(seekTime)
  }

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0

  return (
    <div className={`bg-gradient-to-br from-blue-50 via-cyan-50 to-purple-50 rounded-2xl p-6 border border-blue-100/50 shadow-inner ${className}`}>
      <div className="flex items-center gap-4">
        {/* Play/Pause Button */}
        <Button
          onClick={handlePlay}
          disabled={isLoading}
          className="w-14 h-14 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white flex items-center justify-center p-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border-2 border-white/20"
        >
          {isLoading ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : isPlaying ? (
            <span className="text-sm">⏸️</span>
          ) : (
            <span className="text-sm">▶️</span>
          )}
        </Button>

        {/* Waveform Visualization */}
        <div className="flex-1">
          <div 
            className="flex items-center gap-0.5 h-8 cursor-pointer"
            onClick={(e) => {
              const rect = e.currentTarget.getBoundingClientRect()
              const clickX = e.clientX - rect.left
              handleSeek(clickX, rect.width)
            }}
          >
            {waveform.map((height, index) => {
              const barProgress = (index / waveform.length) * 100
              const isPassed = barProgress <= progress
              const isActive = isPlaying && Math.abs(barProgress - progress) < 2.5
              
              return (
                <div
                  key={index}
                  className={`w-1 rounded-full transition-all duration-100 ${
                    isPassed 
                      ? 'bg-blue-500' 
                      : 'bg-gray-300'
                  } ${
                    isActive ? 'bg-blue-600' : ''
                  }`}
                  style={{
                    height: `${Math.max(4, height * 24 + (isActive ? audioLevel * 8 : 0))}px`,
                  }}
                />
              )
            })}
          </div>
          
          {/* Progress indicator */}
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>{currentTime.toFixed(1)}s</span>
            <span>{duration.toFixed(1)}s</span>
          </div>
        </div>
      </div>
    </div>
  )
}
